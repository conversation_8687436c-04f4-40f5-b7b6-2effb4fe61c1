const script1 = document.createElement('script');
script1.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js';
document.head.appendChild(script1);

const script2 = document.createElement('script');
script2.src = 'https://cdn.jsdelivr.net/npm/tesseract.js@4.0.2/dist/tesseract.min.js';
document.head.appendChild(script2);

let isDragging = false;
let startX, startY;
let selectionBox;

document.addEventListener("contextmenu", e => e.preventDefault()); // block context menu

document.addEventListener("mousedown", (e) => {
  if (e.button === 2) { // right click
    startX = e.pageX;
    startY = e.pageY;
    isDragging = true;

    selectionBox = document.createElement("div");
    selectionBox.style.position = "absolute";
    selectionBox.style.border = "2px dashed #00f";
    selectionBox.style.background = "rgba(0,0,255,0.2)";
    selectionBox.style.left = `${startX}px`;
    selectionBox.style.top = `${startY}px`;
    selectionBox.style.zIndex = "999999";
    document.body.appendChild(selectionBox);
  }
});

document.addEventListener("mousemove", (e) => {
  if (isDragging && selectionBox) {
    const width = e.pageX - startX;
    const height = e.pageY - startY;
    selectionBox.style.width = `${Math.abs(width)}px`;
    selectionBox.style.height = `${Math.abs(height)}px`;
    selectionBox.style.left = `${Math.min(e.pageX, startX)}px`;
    selectionBox.style.top = `${Math.min(e.pageY, startY)}px`;
  }
});

document.addEventListener("mouseup", async (e) => {
  if (isDragging && e.button === 2) {
    isDragging = false;

    const rect = selectionBox.getBoundingClientRect();
    selectionBox.remove();

    html2canvas(document.body, {
      x: rect.left + window.scrollX,
      y: rect.top + window.scrollY,
      width: rect.width,
      height: rect.height,
      scale: 2
    }).then(canvas => {
      Tesseract.recognize(canvas.toDataURL(), 'eng')
        .then(({ data: { text } }) => {
          navigator.clipboard.writeText(text.trim());
          alert("Copied text: " + text.trim());
        });
    });
  }
});
